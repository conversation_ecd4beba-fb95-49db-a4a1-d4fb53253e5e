# Dividend Tracker

## Setup

### Prerequisites

```bash
# Install Node.js package manager
brew install yarn

# Install GitLab CLI for release management
brew install glab

# initial setup
yarn && yarn e2e:install
```

## Development Workflow

### Feature Development

```bash
# Create feature branch
git checkout -b feature/your-feature-name
# ... make changes ...
git push origin feature/your-feature-name
# Create MR to develop
```

**Result**: Creates pre-release `v2.X.0-pre.1` automatically + commits version to develop

### Chore/Bug Fixes

```bash
# Create chore/hotfix branch
git checkout -b chore/fix-something
# ... make changes ...
git push origin chore/fix-something
# Create MR to develop
```

**Result**: Bumps patch version `v2.0.1` + commits version to develop

### Stable Release

```bash
# Create release branch from develop
git checkout develop
git pull origin develop
git checkout -b release/v2.1.0
git push origin release/v2.1.0
# Create MR to master
```

**Result**: Creates stable release `v2.1.0` automatically + commits version to master

### Branch Types & Version Strategy

- `feature/*` → Minor bump + pre-release (e.g. `2.0.0` → `2.1.0-pre.1`)
- `release/vX.Y.Z` → Exact version from branch name (e.g. `release/v2.1.0` → `2.1.0`)
- `chore/*`, `hotfix/*`, other → Patch bump (e.g. `2.0.0` → `2.0.1`)

### Merge Strategy

1. Merge features/chores to `develop` (auto-commits version to develop)
2. Create `release/vX.Y.Z` from `develop` when ready
3. Merge release branch to `master` (auto-commits version to master)
4. Manually rebase `develop` against `master` to sync versions

### Version Commit Behavior

✅ **Auto-commits happen after successful pipeline completion**

- Commits include `[skip ci]` to prevent infinite loops
- Only commits to target branches after merges (not during MR)
- Version changes are permanent in repository

## Post-Release Workflow (macOS DMG)

### Add macOS Support to Existing Release

After CI creates Linux/Windows release, add macOS DMG manually:

```bash
# 1. Checkout the release tag
git checkout v2.X.0

# 2. Install dependencies and build DMG
yarn install --frozen-lockfile
yarn build:mac

# 3. Upload DMG to existing release
glab release upload v2.X.0 dist/*.dmg --name "macOS DMG"

# 4. Verify upload
glab release view v2.X.0
```

### Update Release Notes (Optional)

```bash
# Add macOS installation instructions
glab release edit v2.X.0 --notes "
$(glab release view v2.X.0 --json | jq -r '.description')

### macOS Installation
Download the DMG file and drag the app to your Applications folder.
"
```

**Note**: macOS builds require local Mac hardware due to native dependency limitations.
