import MenuList from '@mui/material/MenuList';
import MenuItem from '@mui/material/MenuItem';
import ListItemIcon from '@mui/material/ListItemIcon';
import ListItemText from '@mui/material/ListItemText';
import { styled } from '@mui/material/styles';
import { ReactNode, useContext } from 'react';
import { NavLink } from 'react-router-dom';

import I18nContext from '@app/contexts/i18n';
import { Divider, DashboardIcon, WorkIcon, HistoryIcon, InsertInvitationIcon, SettingsIcon } from '@app/components/ui';
import { ROUTE_APP } from '@app/lib/constants';
import th from '@app/styles/theme';

const links = [
  { route: ROUTE_APP.PORTFOLIO, i18nKey: 'portfolio', icon: WorkIcon },
  { route: ROUTE_APP.REPORTS, i18nKey: 'reports', icon: HistoryIcon },
  { route: ROUTE_APP.GOALS, i18nKey: 'goals', icon: InsertInvitationIcon },
];

const SidebarMenuItem = ({ iconJsx, isActive, text }: { iconJsx: ReactNode; isActive: boolean; text: string }) => {
  return (
    <MenuItem
      sx={{
        backgroundColor: isActive ? th.colors.greyL : null,
        borderRight: isActive ? `0.3rem solid ${th.colors.primaryBlue}` : '',
        paddingBottom: '1.2rem',
        paddingTop: '1.2rem',
      }}
    >
      <ListItemIcon sx={{ color: isActive ? th.colors.black : th.colors.greyD, fontSize: 24 }}>{iconJsx}</ListItemIcon>
      <ListItemText
        primary={text}
        sx={{
          color: isActive ? th.colors.black : th.colors.greyD,
          ml: 1,
          textTransform: 'uppercase',
        }}
      />
    </MenuItem>
  );
};

const SidebarMenu = () => {
  const { t } = useContext(I18nContext);

  return (
    <>
      <MenuList component="div" sx={{ pl: 0.5, pt: 2, pb: 2 }}>
        <StyledNavLink to={ROUTE_APP.DASHBOARD} draggable="false">
          {({ isActive }) => (
            <SidebarMenuItem iconJsx={<DashboardIcon fontSize="medium" />} isActive={isActive} text={String(t('nav.dashboard'))} />
          )}
        </StyledNavLink>

        <Divider />

        {links.map(({ route, i18nKey, icon: Icon }) => (
          <StyledNavLink key={`nav-${i18nKey}`} to={route} draggable="false">
            {({ isActive }) => (
              <SidebarMenuItem iconJsx={<Icon fontSize="medium" />} isActive={isActive} text={String(t(`nav.${i18nKey}`))} />
            )}
          </StyledNavLink>
        ))}

        <Divider />

        <StyledNavLink to={ROUTE_APP.SETTINGS} draggable="false">
          {({ isActive }) => (
            <SidebarMenuItem iconJsx={<SettingsIcon fontSize="medium" />} isActive={isActive} text={String(t('nav.settings'))} />
          )}
        </StyledNavLink>
      </MenuList>
    </>
  );
};

const StyledNavLink = styled(NavLink)({
  '& .MuiListItemText-root': {
    paddingLeft: 0,
  },
  '& .MuiTypography-root': {
    fontFamily: th.fontFamilies.osSemiBold,
  },
  '& p, & .MuiTypography-body1, & .MuiTypography-body2': {
    fontWeight: 400,
  },
  textTransform: 'capitalize',
});

export default SidebarMenu;
