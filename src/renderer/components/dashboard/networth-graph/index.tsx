import { useContext } from 'react';
import { Line } from 'react-chartjs-2';

import { Title } from '@app/components/ui';
import { I18nContext } from '@app/contexts';
import DataContext, { DataContextState } from '@app/contexts/data';
import months from '@app/lib/months';
import { round, Formatter } from '@app/lib/utils';
import CurrencyService from '@app/services/currency';
const { format } = CurrencyService();
const date = new Date();
const currentYear = date.getFullYear();
const currentMonth = new Date().getMonth();
const colors = {
  grey: '#95a5a6',
  blue: '#03A9F4',
};

type DataSetType = {
  label: string | number;
  data: number[];
  backgroundColor: string;
  borderColor: string;
};

type GraphDataType = { datasets: DataSetType[]; labels: string[] };

const getDataByYear = ({ reports, year }: { reports: ReportType[]; year: number }) => {
  const data = months.map((_, monthIndex) => {
    const report = reports.find((r) => r.month === monthIndex && r.year === year);

    return report ? report.converted.networth : 0;
  });

  if (year >= currentYear) {
    // correct graph for reports created in the future
    let i = data.length - 1;
    do {
      if (i > currentMonth && data[i] === 0) {
        delete data[i];
      } else {
        break;
      }
    } while (i--);
  }

  return data;
};

const DashboardNetworthGraph = () => {
  const { t } = useContext(I18nContext);
  const { reports } = useContext<DataContextState>(DataContext);

  if (!reports || reports.length === 0) {
    return <p>{t('dashboard.noNetworthReport')}</p>;
  }

  const previousYear = currentYear - 1;
  const currentYearData = getDataByYear({ reports, year: currentYear });
  const previousYearData = getDataByYear({ reports, year: previousYear });

  const data: GraphDataType = {
    labels: months.map((_, i) => `${t(`month.${i}`).substring(0, 3)}.`),
    datasets: [],
  };

  if (currentYearData.length) {
    data.datasets.push({
      label: currentYear,
      data: currentYearData,
      backgroundColor: 'transparent',
      borderColor: colors.blue,
    });
  }

  if (previousYearData.length) {
    data.datasets.push({
      label: previousYear,
      data: previousYearData,
      backgroundColor: 'transparent',
      borderColor: colors.grey,
    });
  }

  const options = {
    tooltips: {
      callbacks: {
        label: ({ yLabel }: { yLabel: number }) => ` ${format(round(yLabel, 2).toFixed(2))}`,
      },
    },
    legend: {
      display: true,
      fullWidth: true,
    },
    fill: false,
    elements: {
      line: {
        tension: 0.2,
      },
    },
    responsive: true,
    spanGaps: true,
    scales: {
      xAxes: [
        {
          stacked: true,
          gridLines: {
            display: true,
            offsetGridLines: false,
          },
          ticks: {
            beginAtZero: true,
            maxRotation: 0,
          },
        },
      ],
      yAxes: [
        {
          stacked: false,
          ticks: {
            beginAtZero: true,
            callback: (value: number) => Formatter.price(value),
          },
        },
      ],
    },
  };

  return (
    <>
      <Title gutterBottom variant="h3">
        {t('dashboard.networth')}
      </Title>
      <Line data={data} options={options} height={100} />
    </>
  );
};

export default DashboardNetworthGraph;
