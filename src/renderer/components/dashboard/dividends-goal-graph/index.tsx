import { useContext } from 'react';
import { Doughnut } from 'react-chartjs-2';

import { Title } from '@app/components/ui';
import { I18nContext, DataContext } from '@app/contexts';
import { DIVIDEND_FREQUENCY } from '@app/lib/constants';
import { round, Formatter } from '@app/lib/utils';
import CurrencyService from '@app/services/currency';
const { convert } = CurrencyService();
const grey = '#95a5a6';
const blue = '#03A9F4';
const green = '#55D456';

const calculateYearlyDividends = (holdings: HoldingType[]) => {
  const ret = holdings.reduce((sum: number, { dividendCurrency, count, dividend, dividendFrequency }) => {
    const frequency = dividendFrequency === DIVIDEND_FREQUENCY.MONTHLY ? 12 : 4;
    let dividends = count * dividend * frequency;
    dividends = convert(dividends, { from: dividendCurrency });

    return sum + dividends;
  }, 0);

  return round(ret);
};

type GetYearlyDividendsProps = {
  reports: ReportType[];
  year: number;
};

const getYearlyDividends = ({ reports, year }: GetYearlyDividendsProps) => {
  return reports.filter((report) => report.year === year).reduce((sum, { converted }) => sum + converted.dividends, 0);
};

type DashboardDividendsGoalGraphProps = {
  year: number;
};

const DashboardDividendsGoalGraph = ({ year }: DashboardDividendsGoalGraphProps) => {
  const { t } = useContext(I18nContext);
  const { holdings, reports, goals } = useContext(DataContext);
  const [goal] = goals;

  if (!goal) {
    return <p>{t('dashboard.noDividendGoal')}</p>;
  }

  const dividendsEstimation = calculateYearlyDividends(holdings);
  const currentYearDividendsTotal = round(getYearlyDividends({ reports, year }), 2);
  const currentYearPercentage = round((currentYearDividendsTotal / goal.converted.dividends) * 100);
  const goalPercentage = currentYearPercentage >= 100 ? 100 : 100 - currentYearPercentage;
  const dsPercent1 = currentYearPercentage >= 100 ? 100 : currentYearPercentage;
  const dsPercent2 = currentYearPercentage >= 100 ? 0 : goalPercentage;
  const primaryColor = goalPercentage === 100 ? green : blue;
  const secondaryColor = grey;

  const data = {
    labels: [''],
    datasets: [
      {
        data: [dsPercent1, dsPercent2],
        backgroundColor: [primaryColor, secondaryColor],
        hoverBackgroundColor: [primaryColor, secondaryColor],
        borderWidth: 0,
      },
    ],
    doughnutInnerTextTop: {
      label: t('dashboard.estimated'),
      description: `${Formatter.price(dividendsEstimation)}`,
    },
    doughnutInnerTextBottom: {
      label: year,
      description: `${Formatter.price(currentYearDividendsTotal)} (${currentYearPercentage}%)`,
    },
  };
  const options = {
    responsive: true,
    tooltips: {
      enabled: false,
    },
    percentageInnerCutout: 90,
    cutoutPercentage: 78,
    borderWidth: 1,
    legend: {
      display: false,
    },
    scales: {
      xAxes: [
        {
          display: false,
          ticks: {
            beginAtZero: true,
          },
        },
      ],
      yAxes: [
        {
          display: false,
          ticks: {
            beginAtZero: true,
          },
        },
      ],
    },
  };

  return (
    <>
      <Title gutterBottom textAlign={'center'} variant="h3">
        {t('dashboard.yearlyGoal', { value: goal.formatted.dividends })}
      </Title>
      <Doughnut data={data} options={options} height={200} />
    </>
  );
};

export default DashboardDividendsGoalGraph;
