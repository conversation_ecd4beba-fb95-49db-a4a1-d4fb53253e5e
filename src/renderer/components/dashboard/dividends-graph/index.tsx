import { useContext } from 'react';
import { Line } from 'react-chartjs-2';

import { Title } from '@app/components/ui';
import { round, Formatter } from '@app/lib/utils';
import months from '@app/lib/months';
import { I18nContext, DataContext } from '@app/contexts';
import CurrencyService from '@app/services/currency';
let { format } = CurrencyService();
const date = new Date();
const currentYear = date.getFullYear();
const currentMonth = date.getMonth();
const colors = {
  grey: '#95a5a6',
  blue: '#03A9F4',
};

type DataSet = {
  label: string;
  data: number[];
  backgroundColor: string;
  borderColor: string;
};

type GetDataByYearProps = {
  reports: ReportType[];
  year: number;
};

const getDataByYear = ({ reports, year }: GetDataByYearProps) => {
  const data = months.map((_, monthIndex) => {
    const report = reports.find((r) => r.month === monthIndex && r.year === year);

    return report ? report.converted.dividends : 0;
  });

  if (year >= currentYear) {
    // correct graph for reports created in the future (currently disabled feature)
    let i = data.length - 1;
    do {
      if (i > currentMonth && data[i] === 0) {
        delete data[i];
      } else {
        break;
      }
    } while (i--);
  }

  return data;
};

const DashboardDividendsGraph = () => {
  const { t } = useContext(I18nContext);
  const { reports } = useContext(DataContext);

  if (reports.length === 0) {
    return <p>{t('dashboard.noDividendReport')}</p>;
  }

  const previousYear = currentYear - 1;
  const currentYearData = getDataByYear({ reports, year: currentYear });
  const previousYearData = getDataByYear({ reports, year: previousYear });

  const data: { datasets: DataSet[]; labels: string[] } = {
    datasets: [],
    labels: months.map((_, i) => `${t(`month.${i}`).substring(0, 3)}.`),
  };

  if (currentYearData.length) {
    data.datasets.push({
      label: String(currentYear),
      data: currentYearData,
      backgroundColor: 'transparent',
      borderColor: colors.blue,
    });
  }

  if (previousYearData.length) {
    data.datasets.push({
      label: String(previousYear),
      data: previousYearData,
      backgroundColor: 'transparent',
      borderColor: colors.grey,
    });
  }

  const options = {
    tooltips: {
      callbacks: {
        label: (tooltipItem: { yLabel: number }) => ` ${format(round(tooltipItem.yLabel, 2).toFixed(2))}`,
      },
    },
    legend: {
      display: true,
      fullWidth: true,
    },
    fill: false,
    elements: {
      line: {
        tension: 0.2,
      },
    },
    responsive: true,
    spanGaps: true,
    scales: {
      xAxes: [
        {
          stacked: true,
          gridLines: {
            display: true,
            offsetGridLines: false,
          },
          ticks: {
            beginAtZero: true,
            maxRotation: 0,
          },
        },
      ],
      yAxes: [
        {
          stacked: false,
          ticks: {
            beginAtZero: true,
            callback: (value: number) => Formatter.price(value),
          },
        },
      ],
    },
  };

  return (
    <>
      <Title gutterBottom variant="h3">
        {t('dashboard.dividends')}
      </Title>
      <Line data={data} options={options} height={100} />
    </>
  );
};

export default DashboardDividendsGraph;
