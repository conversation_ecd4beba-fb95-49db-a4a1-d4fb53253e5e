import { useContext } from 'react';
import { Doughnut } from 'react-chartjs-2';

import { Title } from '@app/components/ui';
import { Formatter, round } from '@app/lib/utils';
import { I18nContext, DataContext } from '@app/contexts';
const colors = {
  grey: '#95a5a6',
  blue: '#03A9F4',
  green: '#55D456',
};

const getNetworth = (holdings: HoldingType[]) => {
  const networth = holdings.reduce((sum, { converted }) => sum + converted.valuation, 0);

  return round(networth);
};

const DashboardNetworthGoalGraph = () => {
  const { t } = useContext(I18nContext);
  const { holdings, goals } = useContext(DataContext);
  const [goal] = goals;

  if (!goal) {
    return <p>{t('dashboard.noNetworthGoal')}</p>;
  }

  const networth = getNetworth(holdings);

  const currentYearPercentage = round((networth / goal.converted.networth) * 100);
  const goalPercentage = currentYearPercentage >= 100 ? 100 : 100 - currentYearPercentage;
  const dsPercent1 = currentYearPercentage >= 100 ? 100 : currentYearPercentage;
  const dsPercent2 = currentYearPercentage >= 100 ? 0 : goalPercentage;
  const primaryColor = goalPercentage === 100 ? colors.green : colors.blue;
  const secondaryColor = colors.grey;

  const data = {
    labels: [''],
    datasets: [
      {
        data: [dsPercent1, dsPercent2],
        backgroundColor: [primaryColor, secondaryColor],
        hoverBackgroundColor: [primaryColor, secondaryColor],
        borderWidth: 0,
      },
    ],
    doughnutInnerTextTop: {
      label: t('dashboard.goal'),
      description: Formatter.price(goal.converted.networth),
    },
    doughnutInnerTextBottom: {
      label: t('dashboard.current'),
      description: `${Formatter.price(networth)} (${currentYearPercentage}%)`,
    },
  };
  const options = {
    responsive: true,
    tooltips: {
      enabled: false,
    },
    percentageInnerCutout: 90,
    cutoutPercentage: 78,
    borderWidth: 1,
    legend: {
      display: false,
    },
    scales: {
      xAxes: [
        {
          display: false,
          ticks: {
            beginAtZero: true,
          },
        },
      ],
      yAxes: [
        {
          display: false,
          ticks: {
            beginAtZero: true,
          },
        },
      ],
    },
  };

  return (
    <>
      <Title gutterBottom textAlign={'center'} variant="h3">
        {t('dashboard.termGoal', { value: goal.formatted.networth })}
      </Title>
      <Doughnut data={data} options={options} height={200} />
    </>
  );
};

export default DashboardNetworthGoalGraph;
