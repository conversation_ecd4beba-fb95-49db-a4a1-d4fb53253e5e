import { styled } from '@mui/material/styles';
import { useLocation, Outlet } from 'react-router-dom';
import posed, { PoseGroup } from 'react-pose';

import { Paper } from '@app/components/ui';
import th from '@app/styles/theme';

const RouteContainer = posed.div({
  enter: {
    delay: 50,
    filter: 'blur(0px)',
    opacity: 1,
    transition: {
      default: { duration: 200 },
      y: { type: 'spring', stiffness: 1000, damping: 15 },
    },
    y: 0,
  },
  exit: {
    filter: 'blur(10px)',
    opacity: 0,
    transition: { duration: 50 },
    y: 50,
  },
});

const AuthContainer = () => {
  const location = useLocation();

  return (
    <StyledMain data-testid="auth-container">
      <PoseGroup>
        <RouteContainer key={location.hash}>
          <Paper
            elevation={20}
            sx={{
              alignSelf: 'center',
              left: '50%',
              p: 2,
              position: 'absolute',
              top: '50%',
              transform: 'translate(-50%, -50%)',
              width: 500,
            }}
          >
            <Outlet />
          </Paper>
        </RouteContainer>
      </PoseGroup>
    </StyledMain>
  );
};

const StyledMain = styled('main')`
  align-items: center;
  background-color: ${th.colors.bgBlue};
  display: flex;
  flex-direction: column;
  flex: 1;
  height: 100vh;
  justify-content: center;
  width: 100%;
`;

export default AuthContainer;
