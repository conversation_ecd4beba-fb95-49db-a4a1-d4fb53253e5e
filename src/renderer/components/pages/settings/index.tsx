import { useContext } from 'react';

import I18nContext from '@app/contexts/i18n';
import SettingsForm from '@app/components/settings/form';
import { Box } from '@app/components/ui';
import { StyledPageHeader, StyledPageTitle } from '@app/styles/pages.styles';

const SettingsPage = () => {
  const { t } = useContext(I18nContext);

  return (
    <Box>
      <StyledPageHeader>
        <StyledPageTitle variant="h2">{t('nav.settings')}</StyledPageTitle>
      </StyledPageHeader>

      <SettingsForm />
    </Box>
  );
};

export default SettingsPage;
