import { useContext } from 'react';

import I18nContext from '@app/contexts/i18n';
import GoalList from '@app/components/goals/goal-list';
import { Box } from '@app/components/ui';
import { StyledPageHeader, StyledPageTitle } from '@app/styles/pages.styles';

const GoalsPage = () => {
  const { t } = useContext(I18nContext);

  return (
    <Box>
      <StyledPageHeader>
        <StyledPageTitle variant="h2">{t('nav.goals')}</StyledPageTitle>
      </StyledPageHeader>

      <GoalList />
    </Box>
  );
};

export default GoalsPage;
