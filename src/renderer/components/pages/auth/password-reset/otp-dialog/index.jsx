import React, { useEffect, useContext, useState } from 'react';
import Dialog from '@mui/material/Dialog';
import DialogActions from '@mui/material/DialogActions';
import DialogContent from '@mui/material/DialogContent';
import CircularProgress from '@mui/material/CircularProgress';
import { Navigate } from 'react-router-dom';
import Message from '@app/components/shared/message';
import { ROUTE_AUTH } from '@app/lib/constants';

import AppContext from '@app/contexts/app';
import I18nContext from '@app/contexts/i18n';
import { Button, FormField } from '@app/components/ui';

const getInitialState = () => ({
  otp: '',
});

const AuthPasswordResetOtpDialog = ({ email }) => {
  const { t } = useContext(I18nContext);
  let { actions } = useContext(AppContext);
  let [form, setForm] = useState(getInitialState());
  let [error, setError] = useState(null);
  let [isDisabled, setIsDisabled] = useState(true);
  let [isLoading, setIsLoading] = useState(false);
  let [redirect, setRedirect] = useState(null);
  let [visible, setVisible] = useState(true);

  const handleChange =
    (name) =>
    ({ target: { value } }) => {
      setForm({ ...form, [name]: value });
    };

  const handleCancelClick = (ev) => {
    ev.preventDefault();
    setVisible(false);
  };

  const handleSubmit = (ev) => {
    ev.preventDefault();
    setIsDisabled(true);
    setIsLoading(true);

    actions.auth
      .passwordOtp({ email, otp: form.otp })
      .then(() => {
        setRedirect(ROUTE_AUTH.PASSWORD_CHANGE);
      })
      .catch((error) => {
        console.log(error);

        setError(t(`errors.${error.code}`));
        setIsDisabled(false);
        setIsLoading(false);
      });
  };

  if (redirect) {
    return <Navigate to={redirect} />;
  }

  return (
    <Dialog id="add-report-modal" fullWidth open={visible} onClose={handleCancelClick}>
      <form onSubmit={handleSubmit}>
        <DialogContent>
          {error && (
            <div className="row margin--top">
              <Message className="col-xs-12" type="error">
                {error}
              </Message>
            </div>
          )}

          <div className="row margin--top">
            <div className="col-xs-12">
              <FormField type="text" descriptionBefore={t('auth.otpDescription')} value={form.otp} onInput={handleChange('otp')} />
            </div>
          </div>
        </DialogContent>
        <DialogActions>
          <Button type="submit" endIcon={isLoading ? <CircularProgress /> : null} color="primary" disabled={isDisabled}>
            {t('common.submit')}
          </Button>
          <Button variant="contained" color="secondary" onClick={handleCancelClick}>
            {t('common.cancel')}
          </Button>
        </DialogActions>
      </form>
    </Dialog>
  );
};

export default AuthPasswordResetOtpDialog;
