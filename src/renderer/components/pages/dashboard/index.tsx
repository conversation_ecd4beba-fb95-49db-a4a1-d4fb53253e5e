import { useContext } from 'react';

import { Box, Divider, Grid, Title } from '@app/components/ui';
import I18nContext from '@app/contexts/i18n';
import DashboardDividendsGraph from '@app/components/dashboard/dividends-graph';
import DashboardDividendsGoalGraph from '@app/components/dashboard/dividends-goal-graph';
import DashboardNetworthGraph from '@app/components/dashboard/networth-graph';
import DashboardNetworthGoalGraph from '@app/components/dashboard/networth-goal-graph';
import { StyledPageHeader, StyledPagePaper, StyledPageTitle } from '@app/styles/pages.styles';
const currentYear = new Date().getFullYear();

const Dashboard = () => {
  const { t } = useContext(I18nContext);

  return (
    <Box>
      <StyledPageHeader>
        <StyledPageTitle variant="h2">{t('nav.dashboard')}</StyledPageTitle>
      </StyledPageHeader>

      <StyledPagePaper>
        <Grid container spacing={2} columns={12}>
          <Grid size={{ xs: 12, md: 8 }}>
            <DashboardDividendsGraph />
          </Grid>
          <Grid size={{ xs: 12, md: 4 }}>
            <DashboardDividendsGoalGraph year={currentYear} />
          </Grid>
        </Grid>
      </StyledPagePaper>

      <Box sx={{ m: 1, py: 2 }}>
        <Divider sx={{ borderWidth: 1 }} />
      </Box>

      <StyledPagePaper>
        <Grid container spacing={2} columns={12}>
          <Grid size={{ xs: 12, md: 8 }}>
            <DashboardNetworthGraph />
          </Grid>
          <Grid size={{ xs: 12, md: 4 }}>
            <DashboardNetworthGoalGraph />
          </Grid>
        </Grid>
      </StyledPagePaper>
    </Box>
  );
};

export default Dashboard;
