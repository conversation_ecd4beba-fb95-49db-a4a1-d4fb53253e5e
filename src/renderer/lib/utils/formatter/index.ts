import CurrencyService from '@app/services/currency';
const { format } = CurrencyService();

// const round = (value, precision = 2) => Number(`${Math.round(`${value}e${precision}`)}e-${precision}`);

const round = (value: number, precision = 2) => {
  return Number(Math.round(value * 10 ** precision) / 10 ** precision);
};

const MIN_K = 950;
const MIN_M = 950000;
const MIN_B = 950000000;

const Formatter = {
  price: (value: number) => {
    if (typeof value !== 'number') {
      throw new Error(`Formatter.price() expected a number, but got "${value}" instead`);
    }

    let postLabel = '';

    if (value >= MIN_K && value < MIN_M) {
      value = value / 1000;
      postLabel = 'k';
    } else if (value >= MIN_M && value < MIN_B) {
      value = value / 1000000;
      postLabel = 'M';
    } else if (value >= MIN_B) {
      value = value / 1000000000;
      postLabel = 'B';
    }

    return `${format(round(value, 2))}${postLabel}`;
  },
};

export default Formatter;
