#!/bin/bash

# Cypress Test Runner
# Usage: ./scripts/e2e-docker.sh [local|docker|ui|build|clean]

set -e

# Function to run local e2e tests
run_local_tests() {
    # Create coverage directories
    mkdir -p coverage .nyc_output

    # Start the dev server in the background
    echo "Starting dev server..."
    yarn dev:docker &
    DEV_PID=$!

    # Function to cleanup on exit
    cleanup() {
        echo "Stopping dev server (PID: $DEV_PID)..."
        kill $DEV_PID 2>/dev/null || true
        wait $DEV_PID 2>/dev/null || true
        echo "Dev server stopped."
    }

    # Set up trap to cleanup on script exit
    trap cleanup EXIT INT TERM

    # Wait for the server to be ready
    echo "Waiting for dev server to be ready..."
    npx wait-on http://localhost:5175 --timeout 60000

    # Run the tests
    echo "Running Cypress tests..."
    CYPRESS_baseUrl=http://localhost:5175 npx cypress run --browser chrome

    # Generate coverage report
    echo "Generating coverage report..."
    if [ -f "coverage/coverage-final.json" ]; then
        echo "✅ Coverage data found! Generating reports..."
        npx nyc report --reporter=html --reporter=text
        echo ""
        echo "📊 Coverage Report Summary:"
        echo "- HTML Report: coverage/index.html"
        echo "- JSON Report: coverage/coverage-final.json"
        echo "- Coverage files: $(ls coverage/ | wc -l) files generated"
        
        # Show a quick summary if available
        if [ -f "coverage/coverage-summary.json" ]; then
            echo "- Summary available in coverage/coverage-summary.json"
        fi
    else
        echo "❌ No coverage data found. Coverage may not have been collected properly."
        echo "Checking for coverage files:"
        ls -la .nyc_output/ 2>/dev/null || echo "  No .nyc_output directory"
        ls -la coverage/ 2>/dev/null || echo "  No coverage directory"
    fi

    echo "E2E tests completed successfully!"
}

# Handle command line arguments
case "$1" in
  "local"|"")
    echo "🧪 Running Cypress tests locally..."
    run_local_tests
    ;;
    
  "docker")
    echo "🧪 Running Cypress tests in Docker (headless)..."
    docker-compose -f docker-compose.cypress.yml up --build cypress-tests
    ;;
  
  "ui")
    echo "🖥️ Running Cypress tests in Docker (UI mode)..."
    xhost +local:docker 2>/dev/null || echo "Note: X11 forwarding may not work"
    docker-compose -f docker-compose.cypress.yml up --build cypress-ui
    ;;
    
  "build")
    echo "🔨 Building Cypress Docker image..."
    docker build -f Dockerfile.cypress -t cypress-tests .
    ;;
    
  "clean")
    echo "🧹 Cleaning up Docker resources..."
    docker-compose -f docker-compose.cypress.yml down
    docker rmi cypress-tests 2>/dev/null || true
    ;;
    
  *)
    echo "Usage: $0 [local|docker|ui|build|clean]"
    echo ""
    echo "  local     - Run tests locally with dev server (default)"
    echo "  docker    - Run tests in Docker container (headless)"
    echo "  ui        - Run tests in Docker with Cypress UI (requires X11)"
    echo "  build     - Build Docker image only"
    echo "  clean     - Clean up Docker resources"
    exit 1
    ;;
esac 