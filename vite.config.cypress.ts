import { defineConfig, loadEnv } from 'vite';
import react from '@vitejs/plugin-react';
import { resolve } from 'path';
import tsconfigPaths from 'vite-tsconfig-paths';

export default defineConfig(async ({ mode }) => {
  const env = loadEnv(mode, resolve('src/renderer'), '');

  const plugins = [react(), tsconfigPaths()];

  // Only add istanbul plugin in test mode for coverage
  if (process.env.NODE_ENV === 'development' && process.env.CYPRESS_COVERAGE) {
    const { default: istanbul } = await import('vite-plugin-istanbul');
    plugins.push(
      istanbul({
        include: 'src/**/*.{ts,tsx}',
        exclude: ['node_modules/**/*', 'cypress/**/*', '**/*.spec.ts', '**/*.test.ts', '**/*.d.ts'],
        requireEnv: false,
        forceBuildInstrument: true,
        checkProd: false,
      })
    );
  }

  return {
    root: 'src/renderer',

    plugins,

    define: {
      'process.env': {
        NODE_ENV: JSON.stringify(mode),
        PLATFORM: JSON.stringify(process.platform),
        API_HOST: env.API_HOST,
        API_NAMESPACE: env.API_NAMESPACE,
        ALPHAVANTAGE_HOST: env.ALPHAVANTAGE_HOST,
        ALPHAVANTAGE_KEY: env.ALPHAVANTAGE_KEY,
        MARKETSTACK_HOST: env.MARKETSTACK_HOST,
        MARKETSTACK_KEY: env.MARKETSTACK_KEY,
        SESSION_KEY: env.SESSION_KEY,
      },
    },

    server: {
      port: 5175,
      host: '0.0.0.0',
      strictPort: true,
    },

    publicDir: resolve('src/resources/public'),
  };
});
