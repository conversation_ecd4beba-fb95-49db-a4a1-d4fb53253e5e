Create a sophisticated GitLab pipeline for managing software releases and versioning automatically. The software is an Electron app written in TypeScript. Please, ultrathink about each step and use minimalistic approach, avoid comments and echos, no defensive coding, no fancy icons and reports, if something is missing or if something didn't went as expected, pipeline should fail immediately. The pipeline should include the following steps:

1. Check the branch name and calculate the version number based on the branch name.
   - if branch name doesn't start with "release/" or "feature/" (slash included), then calculate next patch version number based on current version inside package.json:
     - v1.0.1 -> v1.0.2
     - v1.2.0-pre.1 -> v1.2.1-pre.1 or v1.2.0-pre.2 (suggest which one is better and why)
   - jobs to run:
     - install (yarn with frozen lockfile), test (yarn test:ci)
     - commit_version_update (commits the updated version number to the branch)
   - no artifacts to be created to avoid wasting space and time
2. if branch name starts with "feature/":
   - calculate next minor version number based on current version inside package.json:
     - v1.0.1 -> v1.1.0-pre.1
     - v1.2.0-pre.1 -> v1.2.0-pre.2
   - jobs to run:
     - install (yarn with frozen lockfile), test (yarn test:ci)
     - commit_version_update (commits the updated version number to the branch)
   - no artifacts to be created to avoid wasting space and time
3. if branch name starts with "release/":
   - calculate next minor version number based on current pre-release version inside package.json:
     - v1.2.0-pre.123 -> v1.2.0
   - jobs to run:
     - install (yarn with frozen lockfile)
     - test (`yarn test:ci`)
     - package (`yarn pack`) - will package the app into a distributable format for linux (appimage) and windows (installer and portable executable)
     - release (creates a tag and release on GitHub with the built artifacts attached)
     - commit_version_update (commits the updated version number to the branch)
   - artifacts to be created: linux and windows distributables

Notes:
_ - version should be updated only when merged into `master` or `develop`, merges into any other branch should not touch the version and it is expected that `master` version and `develop` version are not in sync
_ - `feature/` branches will always be merged into `develop`
_ - `release/` branches will always be merged from `develop` to `master`, so the version commit needs to be only on `master` branch
_ - make sure pipeline is optimized and doesn't install multiple times, and version is calculated early and used in all jobs (where necessary), etc.

here is expected git workflow info if it helps with pipline design:

### Chores/Bug Fixes/Tweaks

```bash
# Create branch which doesn't start with `feature/` or `release/`
git checkout -b chore/fix-something
# ... make changes ...
git push origin chore/fix-something
# Create MR to develop or master
```

**Result**: Bumps patch version (`v2.0.1` or `v2.0.1-pre.1` depending on previous version and pre-release status) + commits version to develop/master (depending on target branch of MR)

### Feature Development

```bash
# Create feature branch
git checkout -b feature/your-feature-name
# ... make changes ...
git push origin feature/your-feature-name
# Create MR to develop
```

**Result**: Creates pre-release (`v2.X.0-pre.1` or `v2.X.23-pre.2` depending on previous version) automatically + commits version to develop (features must be merged into develop only)

### Stable Release

```bash
# Create release branch from develop
git checkout develop
git pull origin develop
git checkout -b release/v2.1.0
git push origin release/v2.1.0
# Create MR to master
```

**Result**: Creates tag and stable release `v2.1.0` automatically + commits version to master

then develop branch is rebased on top of master to keep it in sync and then new feature branches are created from develop again.
