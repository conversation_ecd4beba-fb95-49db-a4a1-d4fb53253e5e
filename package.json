{"displayName": "Dividend Tracker", "name": "dividend-tracker", "description": "Track your dividend income and portfolio performance.", "version": "2.0.1", "main": "./node_modules/.dev/main/index.js", "resources": "src/resources", "homepage": "https://github.com/your-username/dividend-tracker", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "scripts": {"dev": "cross-env NODE_ENV=development electron-vite dev --watch", "start": "cross-env NODE_ENV=production electron-vite preview", "build": "cross-env NODE_ENV=production ./node_modules/.bin/electron-builder --publish never", "build:all": "cross-env NODE_ENV=production ./node_modules/.bin/electron-builder --linux --win --publish never", "build:lin": "cross-env NODE_ENV=production ./node_modules/.bin/electron-builder --linux --publish never", "build:win": "cross-env NODE_ENV=production ./node_modules/.bin/electron-builder --win --publish never", "release": "cross-env NODE_ENV=production electron-builder --publish always", "compile:app": "electron-vite build", "compile:package": "node ./bin/modules/postbuild/index.js", "prebuild": "run-s compile:app compile:package", "postinstall": "run-s prebuild install:deps", "install:deps": "electron-builder install-app-deps", "make:release": "node ./bin/modules/release/index.js", "prepare": "husky", "test": "jest --watch --coverage", "test:ci": "jest --coverage --watchAll=false"}, "dependencies": {"@dnd-kit/core": "^6.3.1", "@dnd-kit/sortable": "^10.0.0", "@emotion/css": "^11.13.5", "@emotion/react": "^11.14.0", "@emotion/styled": "^11.14.0", "@mui/icons-material": "^7.1.0", "@mui/material": "^7.1.0", "array-move": "^4.0.0", "chart.js": "^2.9.3", "classnames": "^2.5.1", "is-online": "^11.0.0", "iscroll": "^5.2.0", "react": "^19.1.0", "react-chartjs-2": "^2.9.0", "react-dom": "^19.1.0", "react-iscroll": "^2.0.3", "react-pose": "^4.0.10", "react-router-dom": "^7.6.1", "styled-reset": "^4.5.2"}, "devDependencies": {"@commitlint/cli": "^19.8.1", "@commitlint/config-conventional": "^19.8.1", "@jest/globals": "^30.0.0-beta.3", "@testing-library/dom": "^10.4.0", "@testing-library/jest-dom": "^6.6.3", "@testing-library/react": "^16.3.0", "@testing-library/user-event": "^14.6.1", "@types/jest": "^29.5.14", "@types/node": "^22.15.29", "@types/react": "^19.1.6", "@types/react-dom": "^19.1.5", "@typescript-eslint/eslint-plugin": "^8.33.0", "@typescript-eslint/parser": "^8.33.0", "@vitejs/plugin-react": "^4.5.0", "cross-env": "^7.0.3", "electron": "^36.3.2", "electron-builder": "^26.0.12", "electron-extension-installer": "^1.2.0", "electron-vite": "^3.1.0", "eslint": "^9.28.0", "eslint-config-prettier": "^10.1.5", "eslint-plugin-node": "^11.1.0", "eslint-plugin-prettier": "^5.4.1", "husky": "^9.1.7", "identity-obj-proxy": "^3.0.0", "jest": "^29.7.0", "jest-environment-jsdom": "^30.0.0-beta.3", "lint-staged": "^16.1.0", "npm-run-all": "^4.1.5", "open": "10.1.2", "prettier": "^3.5.3", "rollup-plugin-inject-process-env": "^1.3.1", "semver": "^7.7.2", "ts-jest": "^29.3.4", "typescript": "^5.8.3", "vite": "^6.3.5", "vite-tsconfig-paths": "^5.1.4"}, "lint-staged": {"*.{js,ts}": ["eslint --quiet --fix"]}, "eslintIgnore": ["dist"]}