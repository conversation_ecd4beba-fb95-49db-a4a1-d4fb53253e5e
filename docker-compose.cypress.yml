version: '3.8'

services:
  cypress-tests:
    build:
      context: .
      dockerfile: Dockerfile.cypress
      cache_from:
        - cypress-tests:latest
    image: cypress-tests:latest
    volumes:
      # Persist node_modules for faster rebuilds
      - cypress_node_modules:/app/node_modules
      # Persist built assets
      - cypress_build:/app/node_modules/.dev
      # Artifacts
      - ./cypress/videos:/app/cypress/videos
      - ./cypress/screenshots:/app/cypress/screenshots
      - ./test-results:/app/test-results
      # Code coverage output
      - ./coverage:/app/coverage
      - ./.c8_output:/app/.c8_output
      # Live source code for development
      - ./cypress:/app/cypress:ro
      - ./src:/app/src:ro
    environment:
      - CI=true
      - CYPRESS_baseUrl=http://localhost:5175
    networks:
      - cypress-net

  # Development mode with live reload
  cypress-ui:
    build:
      context: .
      dockerfile: Dockerfile.cypress
      cache_from:
        - cypress-tests:latest
    image: cypress-tests:latest
    volumes:
      # Persist node_modules for faster rebuilds
      - cypress_node_modules:/app/node_modules
      # Persist built assets
      - cypress_build:/app/node_modules/.dev
      # Live source code
      - ./cypress:/app/cypress
      - ./src:/app/src:ro
      # Artifacts
      - ./cypress/videos:/app/cypress/videos
      - ./cypress/screenshots:/app/cypress/screenshots
      # Code coverage output
      - ./coverage:/app/coverage
      - ./.c8_output:/app/.c8_output
      # X11 for UI
      - /tmp/.X11-unix:/tmp/.X11-unix:rw
    environment:
      - DISPLAY=${DISPLAY}
      - CI=false
      - CYPRESS_baseUrl=http://localhost:5175
    command: yarn e2e:ui:docker
    networks:
      - cypress-net

volumes:
  cypress_node_modules:
  cypress_build:

networks:
  cypress-net:
    driver: bridge
