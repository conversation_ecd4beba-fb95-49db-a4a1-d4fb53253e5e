describe('Dividend Tracker App', () => {
  beforeEach(() => {
    cy.startElectronApp();
  });

  it('should load the application', () => {
    // Wait for React app to render
    cy.get('app', { timeout: 10000 }).should('exist');
    cy.get('app').should('be.visible');
    cy.get('app').should('not.be.empty');
    cy.get('[data-testid="auth-container"]', { timeout: 10000 }).should('exist');
  });
});
