import '@cypress/code-coverage/support';

// Custom commands can be added here
// Example: Cypress.Commands.add('login', (email, password) => { ... })

// Electron app helpers
Cypress.Commands.add('startElectronApp', () => {
  // Visit the base URL (configured in cypress.config.ts)
  cy.visit('/');
});

declare global {
  namespace Cypress {
    interface Chainable {
      startElectronApp(): Chainable<void>;
    }
  }
}
