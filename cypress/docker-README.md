# Cypress Docker Setup

## Quick Start

```bash
./scripts/e2e-docker.sh
```

Expected: Tests run in isolated Docker container with internal dev server. Coverage reports in `./coverage/`.

## Commands

```bash
./scripts/e2e-docker.sh headless    # Run tests (default)
./scripts/e2e-docker.sh ui          # Open Cypress UI
./scripts/e2e-docker.sh build       # Build image only
./scripts/e2e-docker.sh clean       # Clean up
```

## Direct Docker

```bash
docker-compose -f docker-compose.cypress.yml up --build cypress-tests
```

## CI Usage

Automatically runs in GitLab CI with `cypress/browsers` image.

## Common Issues

| Problem          | Solution                     |
| ---------------- | ---------------------------- |
| Build fails      | Run `yarn install` first     |
| UI won't show    | Linux: `xhost +local:docker` |
| Dev server fails | Check Docker logs            |

## Done.
