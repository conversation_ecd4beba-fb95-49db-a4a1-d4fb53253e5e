# Cypress E2E Testing

## Quick Start

```bash
yarn e2e:install
yarn dev                # Start dev server first!
# In another terminal:
yarn e2e:ui            # Open Cypress UI
```

Expected: Cypress opens, tests pass.

**⚠️ Must run `yarn dev` first or tests will fail!**

## Commands

```bash
yarn e2e        # Run headless
yarn e2e:ui     # Open Cypress UI
```

## Add Tests

Create `cypress/e2e/feature.cy.ts`:

```typescript
describe('Feature', () => {
  it('works', () => {
    cy.startElectronApp();
    cy.get('body').should('exist');
  });
});
```

## Common Issues

| Problem       | Solution                     |
| ------------- | ---------------------------- |
| ECONNREFUSED  | Run `yarn dev` first         |
| App not found | Check dev server on :5173    |
| Test fails    | Check `cypress/screenshots/` |
| Slow tests    | Add `cy.wait(1000)`          |

## Done.
