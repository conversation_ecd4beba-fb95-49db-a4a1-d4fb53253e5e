stages:
  - version
  - build
  - test
  - package
  - release
  - commit

variables:
  YARN_CACHE_FOLDER: .yarn-cache

cache:
  paths:
    - node_modules/
    - .yarn-cache/

calculate_version:
  stage: version
  image: node:22-alpine
  script:
    - apk add --no-cache git jq
    - CURRENT_VERSION=$(jq -r '.version' package.json)
    - |
      if [[ "$CI_COMMIT_REF_NAME" == release/* ]]; then
        NEW_VERSION=$(echo $CURRENT_VERSION | sed 's/-pre\.[0-9]*$//')
        PIPELINE_TYPE="release"
      elif [[ "$CI_COMMIT_REF_NAME" == feature/* ]]; then
        if [[ "$CURRENT_VERSION" == *"-pre."* ]]; then
          BASE_VERSION=$(echo $CURRENT_VERSION | cut -d'-' -f1)
          PRE_NUM=$(echo $CURRENT_VERSION | grep -o 'pre\.[0-9]*' | cut -d'.' -f2)
          NEW_PRE_NUM=$((PRE_NUM + 1))
          NEW_VERSION="${BASE_VERSION}-pre.${NEW_PRE_NUM}"
        else
          IFS='.' read -ra VERSION_PARTS <<< "$CURRENT_VERSION"
          MAJOR=${VERSION_PARTS[0]#v}
          MINOR=${VERSION_PARTS[1]}
          NEW_MINOR=$((MINOR + 1))
          NEW_VERSION="${MAJOR}.${NEW_MINOR}.0-pre.1"
        fi
        PIPELINE_TYPE="feature"
      else
        if [[ "$CURRENT_VERSION" == *"-pre."* ]]; then
          BASE_VERSION=$(echo $CURRENT_VERSION | cut -d'-' -f1)
          PRE_NUM=$(echo $CURRENT_VERSION | grep -o 'pre\.[0-9]*' | cut -d'.' -f2)
          NEW_PRE_NUM=$((PRE_NUM + 1))
          NEW_VERSION="${BASE_VERSION}-pre.${NEW_PRE_NUM}"
        else
          IFS='.' read -ra VERSION_PARTS <<< "$CURRENT_VERSION"
          MAJOR=${VERSION_PARTS[0]#v}
          MINOR=${VERSION_PARTS[1]}
          PATCH=${VERSION_PARTS[2]}
          NEW_PATCH=$((PATCH + 1))
          NEW_VERSION="${MAJOR}.${MINOR}.${NEW_PATCH}"
        fi
        PIPELINE_TYPE="patch"
      fi
    - echo "NEW_VERSION=$NEW_VERSION" > version.env
    - echo "PIPELINE_TYPE=$PIPELINE_TYPE" >> version.env
    - echo "TARGET_BRANCH=$CI_MERGE_REQUEST_TARGET_BRANCH_NAME" >> version.env
  artifacts:
    reports:
      dotenv: version.env
  only:
    - merge_requests
    - master
    - develop

install:
  stage: build
  image: node:22-alpine
  script:
    - yarn install --frozen-lockfile
  needs:
    - calculate_version

test:
  stage: test
  image: node:22-alpine
  script:
    - yarn test:ci
  needs:
    - install

package:
  stage: package
  image: electronuserland/builder:wine
  before_script:
    - apt-get update && apt-get install -y python3 make g++ libx11-dev libxkbfile-dev libsecret-dev
  script:
    - yarn build:lin
    - yarn build:win
  artifacts:
    paths:
      - dist/*.AppImage
      - dist/*.exe
      - dist/*.zip
    expire_in: 1 week
  needs:
    - test
  only:
    variables:
      - $PIPELINE_TYPE == "release"

github_release:
  stage: release
  image: alpine:latest
  before_script:
    - apk add --no-cache curl jq git
  script:
    - |
      if [[ "$PIPELINE_TYPE" == "release" ]]; then
        echo '{"tag_name":"v'$NEW_VERSION'","target_commitish":"master","name":"v'$NEW_VERSION'","body":"Release v'$NEW_VERSION'","draft":false,"prerelease":false}' > release.json
        RELEASE_RESPONSE=$(curl -s -X POST -H "Authorization:token $GITHUB_TOKEN" -H "Content-Type:application/json" -d @release.json "https://api.github.com/repos/$CI_PROJECT_PATH/releases")
        UPLOAD_URL=$(echo $RELEASE_RESPONSE | jq -r '.upload_url' | sed 's/{?name,label}//')
        for file in dist/*; do
          if [ -f "$file" ]; then
            filename=$(basename "$file")
            curl -s -X POST -H "Authorization:token $GITHUB_TOKEN" -H "Content-Type:application/octet-stream" --data-binary @"$file" "${UPLOAD_URL}?name=${filename}"
          fi
        done
        git config --global user.email "<EMAIL>"
        git config --global user.name "GitLab CI"
        git tag "v$NEW_VERSION"
        git push origin "v$NEW_VERSION"
      fi
  needs:
    - package
  only:
    variables:
      - $PIPELINE_TYPE == "release"

commit_version:
  stage: commit
  image: alpine/git:latest
  before_script:
    - git config --global user.email "<EMAIL>"
    - git config --global user.name "GitLab CI"
  script:
    - apk add --no-cache jq
    - |
      if [[ "$CI_PIPELINE_SOURCE" == "merge_request_event" ]]; then
        exit 0
      fi
      if [[ "$CI_COMMIT_REF_NAME" == "master" || "$CI_COMMIT_REF_NAME" == "develop" ]]; then
        git clone https://gitlab-ci-token:${CI_JOB_TOKEN}@${CI_SERVER_HOST}/${CI_PROJECT_PATH}.git repo
        cd repo
        git checkout $CI_COMMIT_REF_NAME
        jq --arg version "$NEW_VERSION" '.version = $version' package.json > package.json.tmp
        mv package.json.tmp package.json
        git add package.json
        git commit -m "chore: bump version to $NEW_VERSION [skip ci]"
        git push origin HEAD
      fi
  needs:
    - job: test
      artifacts: false
    - job: github_release
      artifacts: false
      optional: true
  only:
    - master
    - develop
  when: on_success
