import reactPlugin from '@vitejs/plugin-react';
import { defineConfig, externalizeDepsPlugin } from 'electron-vite';
import { resolve, normalize, dirname } from 'path';
import injectProcessEnvPlugin from 'rollup-plugin-inject-process-env';
import { loadEnv } from 'vite';
import tsconfigPathsPlugin from 'vite-tsconfig-paths';

import { main, resources } from './package.json';

const [nodeModules, devFolder] = normalize(dirname(main)).split(/\/|\\/g);
const devPath = [nodeModules, devFolder].join('/');

const tsconfigPaths = tsconfigPathsPlugin({
  projects: [resolve('tsconfig.json')],
});

export default defineConfig(({ mode }) => {
  // Load env file based on mode
  const env = loadEnv(mode, resolve('src/renderer'), '');

  return {
    main: {
      plugins: [tsconfigPaths, externalizeDepsPlugin()],

      build: {
        rollupOptions: {
          input: {
            index: resolve('src/main/index.ts'),
          },

          output: {
            dir: resolve(devPath, 'main'),
          },
        },
      },
    },

    renderer: {
      define: {
        'process.env': {
          NODE_ENV: JSON.stringify(mode),
          PLATFORM: JSON.stringify(process.platform),
          API_HOST: env.API_HOST,
          API_NAMESPACE: env.API_NAMESPACE,
          ALPHAVANTAGE_HOST: env.ALPHAVANTAGE_HOST,
          ALPHAVANTAGE_KEY: env.ALPHAVANTAGE_KEY,
          MARKETSTACK_HOST: env.MARKETSTACK_HOST,
          MARKETSTACK_KEY: env.MARKETSTACK_KEY,
          SESSION_KEY: env.SESSION_KEY,
        },
      },

      server: {
        port: process.env.VITE_DEV_SERVER_PORT ? parseInt(process.env.VITE_DEV_SERVER_PORT) : 4927,
        host: process.env.VITE_DEV_SERVER_HOST || '127.0.0.1',
      },

      plugins: [tsconfigPaths, reactPlugin()],
      publicDir: resolve(resources, 'public'),

      build: {
        outDir: resolve(devPath, 'renderer'),

        rollupOptions: {
          plugins: [
            injectProcessEnvPlugin({
              NODE_ENV: mode,
              platform: process.platform,
              ...env,
            }),
          ],

          input: {
            index: resolve('src/renderer/index.html'),
          },

          output: {
            dir: resolve(devPath, 'renderer'),
          },
        },
      },
    },
  };
});
