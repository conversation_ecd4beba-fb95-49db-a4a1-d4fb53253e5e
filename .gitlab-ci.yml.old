stages:
  - prepare
  - build
  - test
  - package
  - release
  - version_commit

variables:
  NODE_VERSION: '20'
  YARN_CACHE_FOLDER: '.yarn-cache'
  # Optimization: Enable yarn network concurrency
  YARN_NETWORK_CONCURRENCY: '8'
  # Use faster registry for dependencies
  YARN_REGISTRY: 'https://registry.yarnpkg.com'

# Optimized cache templates
.cache_pull: &cache_pull
  cache:
    key: '$CI_COMMIT_REF_SLUG-v2'
    paths:
      - node_modules/
      - .yarn-cache/
    policy: pull

.cache_push: &cache_push
  cache:
    key: '$CI_COMMIT_REF_SLUG-v2'
    paths:
      - node_modules/
      - .yarn-cache/
    policy: pull-push

# Shared preparation job to install dependencies once
prepare:
  stage: prepare
  image: node:${NODE_VERSION}-alpine
  <<: *cache_push
  before_script:
    - apk add --no-cache git
    - yarn config set cache-folder $YARN_CACHE_FOLDER
    - yarn config set registry $YARN_REGISTRY
    - yarn config set network-concurrency $YARN_NETWORK_CONCURRENCY
  script:
    - yarn install --frozen-lockfile --prefer-offline
  artifacts:
    paths:
      - node_modules/
    expire_in: 2 hours
  only:
    - master
    - develop
    - /^feature\/.+$/
    - /^release\/.+$/
    - /^hotfix\/.+$/
    - /^[^\/]+\/.+$/

# Combined build and version job for better efficiency
build_and_version:
  stage: build
  image: node:${NODE_VERSION}-alpine
  <<: *cache_pull
  dependencies:
    - prepare
  before_script:
    - apk add --no-cache git
    - git config --global user.email "<EMAIL>"
    - git config --global user.name "GitLab CI"
  script:
    # Build the application
    - yarn compile:app
    - yarn compile:package

    # Version management (moved inline to save job overhead)
    - |
      CURRENT_VERSION=$(node -p "require('./package.json').version")
      echo "Current version: $CURRENT_VERSION"

      if [[ "$CI_COMMIT_REF_NAME" =~ ^feature/.+ ]]; then
        BASE_VERSION=$(yarn --silent semver $CURRENT_VERSION -i minor)
        PRE_NUMBER=$CI_PIPELINE_IID
        NEW_VERSION="${BASE_VERSION}-pre.${PRE_NUMBER}"
        VERSION_TYPE="prerelease"
        RELEASE_TYPE="pre-release"
        TARGET_BRANCH="develop"
        
      elif [[ "$CI_COMMIT_REF_NAME" =~ ^release/.+ ]]; then
        BRANCH_VERSION=$(echo "$CI_COMMIT_REF_NAME" | sed 's/release\/v\?//')
        if [[ "$BRANCH_VERSION" =~ ^[0-9]+\.[0-9]+\.[0-9]+$ ]]; then
          NEW_VERSION="$BRANCH_VERSION"
          VERSION_TYPE="release"
          RELEASE_TYPE="stable"
          TARGET_BRANCH="master"
        else
          echo "Invalid version format in branch name. Expected: release/v2.1.0"
          exit 1
        fi
        
      else
        NEW_VERSION=$(yarn --silent semver $CURRENT_VERSION -i patch)
        VERSION_TYPE="patch"
        RELEASE_TYPE="none"
        TARGET_BRANCH="develop"
      fi

      echo "New version: $NEW_VERSION"
      echo "VERSION_TYPE=$VERSION_TYPE" >> version.env
      echo "NEW_VERSION=$NEW_VERSION" >> version.env
      echo "CURRENT_VERSION=$CURRENT_VERSION" >> version.env
      echo "RELEASE_TYPE=$RELEASE_TYPE" >> version.env
      echo "TARGET_BRANCH=$TARGET_BRANCH" >> version.env

      yarn version --new-version $NEW_VERSION --no-git-tag-version
      echo "{\"version\": \"$NEW_VERSION\", \"type\": \"$VERSION_TYPE\", \"releaseType\": \"$RELEASE_TYPE\", \"previous\": \"$CURRENT_VERSION\"}" > version-info.json
  artifacts:
    reports:
      dotenv: version.env
    paths:git
      - node_modules/.dev/
      - package.json
      - version-info.json
    expire_in: 2 hours
  only:
    - master
    - develop
    - /^feature\/.+$/
    - /^release\/.+$/
    - /^hotfix\/.+$/
    - /^[^\/]+\/.+$/

# Optimized Linux build
package:linux:
  stage: package
  image: electronuserland/builder:wine
  <<: *cache_pull
  dependencies:
    - build_and_version
  script:
    # Skip yarn install since we have node_modules from artifacts
    - yarn build:lin
  artifacts:
    paths:
      - dist/*.AppImage
      - dist/*.tar.gz
    expire_in: 1 week
  only:
    - /^feature\/.+$/
    - /^release\/.+$/

# Optimized Windows build
package:windows:
  stage: package
  image: electronuserland/builder:wine
  <<: *cache_pull
  dependencies:
    - build_and_version
  script:
    # Skip yarn install since we have node_modules from artifacts
    - yarn build:win
  artifacts:
    paths:
      - dist/*.exe
      - dist/*.zip
    expire_in: 1 week
  only:
    - /^feature\/.+$/
    - /^release\/.+$/

# Streamlined release job
release:
  stage: release
  image: registry.gitlab.com/gitlab-org/release-cli:v0.15.0
  dependencies:
    - build_and_version
    - package:linux
    - package:windows
  before_script:
    - apk add --no-cache jq git
  rules:
    # Only run release job for feature branches when merged to develop/master (not in MR)
    - if: '$CI_COMMIT_REF_NAME =~ /^feature\/.+/ && $CI_PIPELINE_SOURCE != "merge_request_event"'
      when: on_success
    # Only run release job for release branches when merged to master (not in MR)
    - if: '$CI_COMMIT_REF_NAME =~ /^release\/.+/ && $CI_PIPELINE_SOURCE != "merge_request_event"'
      when: on_success
    - when: never
  script:
    - |
      VERSION_INFO=$(cat version-info.json)
      NEW_VERSION=$(echo $VERSION_INFO | jq -r '.version')
      RELEASE_TYPE=$(echo $VERSION_INFO | jq -r '.releaseType')

      LINUX_APPIMAGE=$(find dist/ -name "*.AppImage" | head -1)
      WINDOWS_EXE=$(find dist/ -name "*.exe" | head -1)

      if [ "$RELEASE_TYPE" = "pre-release" ]; then
        DESCRIPTION="🧪 Pre-Release v${NEW_VERSION} - Latest features from $(echo $CI_COMMIT_REF_NAME | sed 's/feature\///') branch for testing."
      else
        DESCRIPTION="🚀 Release v${NEW_VERSION} - Stable production release with new features and improvements."
      fi

      echo "RELEASE_DESCRIPTION<<EOF" >> release.env
      echo "$DESCRIPTION" >> release.env
      echo "EOF" >> release.env
  artifacts:
    reports:
      dotenv: release.env
  release:
    name: 'Release v$NEW_VERSION'
    description: '$RELEASE_DESCRIPTION'
    tag_name: 'v$NEW_VERSION'
  after_script:
    # Create git tag only after successful release
    - git config --global user.email "<EMAIL>"
    - git config --global user.name "GitLab CI"
    - git remote set-url origin https://gitlab-ci-token:${CI_JOB_TOKEN}@${CI_SERVER_HOST}/${CI_PROJECT_PATH}.git
    - git tag "v$NEW_VERSION" || echo "Tag may already exist"
    - git push origin --tags

# Lightweight notification for development builds
notify:patch:
  stage: release
  image: alpine:3.18
  dependencies:
    - build_and_version
  before_script:
    - apk add --no-cache jq
  script:
    - |
      VERSION_INFO=$(cat version-info.json)
      NEW_VERSION=$(echo $VERSION_INFO | jq -r '.version')
      RELEASE_TYPE=$(echo $VERSION_INFO | jq -r '.releaseType')

      echo "✅ Build v${NEW_VERSION} completed successfully"
      echo "📦 Build artifacts available (no packages created)"
      echo "Branch: $CI_COMMIT_REF_NAME | Commit: $CI_COMMIT_SHORT_SHA"
  only:
    - master
    - develop
    - /^hotfix\/.+$/
    - /^[^\/]+\/.+$/

# Commit version changes back to repository (runs at the end after success)
commit_version:
  stage: version_commit
  image: node:${NODE_VERSION}-alpine
  dependencies:
    - build_and_version
  before_script:
    - apk add --no-cache git
    - git config --global user.email "<EMAIL>"
    - git config --global user.name "GitLab CI"
  script:
    - |
      # Only commit for merged branches (not the source branch)
      if [[ "$CI_PIPELINE_SOURCE" == "merge_request_event" ]]; then
        echo "Skipping version commit for MR - will commit after merge"
        exit 0
      fi

      # Set up git authentication with personal access token
      git remote set-url origin https://oauth2:${GITLAB_TOKEN}@${CI_SERVER_HOST}/${CI_PROJECT_PATH}.git

      # Check if version actually changed
      if [[ "$NEW_VERSION" == "$CURRENT_VERSION" ]]; then
        echo "No version change needed"
        exit 0
      fi

      echo "Committing version $NEW_VERSION to $TARGET_BRANCH branch"

      # Add and commit the version change
      git add package.json
      git commit -m "chore: bump version to $NEW_VERSION [skip ci]"

      # Push to target branch
      git push origin HEAD:$TARGET_BRANCH

      echo "✅ Version $NEW_VERSION committed to $TARGET_BRANCH"
  rules:
    # Only commit versions for target branches after merge
    - if: '$CI_COMMIT_REF_NAME == "develop"'
      when: on_success
    - if: '$CI_COMMIT_REF_NAME == "master"'
      when: on_success
    - when: never

# E2E testing job
e2e_tests:
  stage: test
  image: cypress/browsers:node22.15.0-chrome122
  dependencies:
    - prepare
  before_script:
    - yarn config set cache-folder $YARN_CACHE_FOLDER
    - yarn config set registry $YARN_REGISTRY
  script:
    - yarn compile:app && yarn compile:package
    - yarn e2e:docker
  artifacts:
    when: always
    paths:
      - cypress/videos
      - cypress/screenshots
    expire_in: 1 week
  only:
    - merge_requests
    - master
    - develop
    - /^feature\/.+$/
    - /^release\/.+$/
