FROM cypress/browsers:node22.15.0-chrome122

WORKDIR /app

# Install global dependencies first (rarely change)
RUN yarn global add wait-on

# Copy package files only (for better layer caching)
COPY package.json yarn.lock ./

# Install dependencies (this layer will be cached unless package.json changes)
RUN yarn install --frozen-lockfile --prefer-offline && yarn cache clean

# Copy source code (this layer changes frequently)
COPY . .

# Build the app (only when source changes)
RUN yarn compile:app && yarn compile:package

# Expose ports
EXPOSE 5174

# Default command
CMD ["yarn", "e2e:docker"] 