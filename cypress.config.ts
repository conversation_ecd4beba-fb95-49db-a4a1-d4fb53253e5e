import { defineConfig } from 'cypress';

export default defineConfig({
  e2e: {
    baseUrl: 'http://localhost:5175',
    supportFile: 'cypress/support/e2e.ts',
    specPattern: 'cypress/e2e/**/*.cy.{js,jsx,ts,tsx}',
    videosFolder: 'cypress/videos',
    screenshotsFolder: 'cypress/screenshots',
    downloadsFolder: 'cypress/downloads',

    setupNodeEvents(on, config) {
      require('@cypress/code-coverage/task')(on, config);

      // Code coverage configuration
      on('task', {
        coverage: require('@cypress/code-coverage/task'),
      });

      // Set coverage options
      config.env.coverage = true;
      config.env.codeCoverage = {
        url: 'http://localhost:5175/__coverage__',
      };

      return config;
    },
  },

  component: {
    devServer: {
      framework: 'react',
      bundler: 'vite',
    },
  },

  // Electron configuration
  env: {
    electronPath: './node_modules/.dev/main/index.js',
  },

  video: true,
  screenshotOnRunFailure: true,

  viewportWidth: 1280,
  viewportHeight: 720,

  defaultCommandTimeout: 10000,
  requestTimeout: 10000,
  responseTimeout: 10000,
});
